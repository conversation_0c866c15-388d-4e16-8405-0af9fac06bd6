# Python 代码运行器组件使用指南

## 概述

我们已经成功将 Python 代码运行器模块化为一个独立的 Livewire 组件 (`resources/views/livewire/run.blade.php`)，可以在任何页面中重复使用，并支持传入课程特定的初始代码。

## 组件文件结构

```
resources/views/livewire/
├── run.blade.php          # 独立的 Python 运行器组件
└── course.blade.php       # 课程页面（使用运行器组件）
```

## 基本使用方法

### 1. 简单调用（使用默认代码）

```blade
@livewire('run')
```

### 2. 传入自定义初始代码

```blade
@livewire('run', ['initialCode' => $customCode])
```

### 3. 在课程页面中使用（当前实现）

```blade
@livewire('run', ['initialCode' => $this->getCourseSpecificCode()])
```

## 组件参数

### `initialCode` (可选)
- **类型**: `string`
- **默认值**: 组件内置的默认示例代码
- **描述**: 编辑器中显示的初始 Python 代码

## 组件功能特性

### ✅ 核心功能
- **代码编辑器**: 支持多行 Python 代码输入
- **实时执行**: 使用 Pyodide 在浏览器中运行 Python
- **输出显示**: 实时显示执行结果和错误信息
- **状态管理**: 显示环境加载和代码执行状态

### ✅ 用户交互
- **键盘快捷键**: Ctrl+Enter / Cmd+Enter 快速运行
- **重置功能**: 恢复到初始代码
- **清空输出**: 清除执行结果
- **示例代码**: 四个预设示例按钮

### ✅ 技术特性
- **独立性**: 完全自包含，包含所有必要的 CSS/JS
- **可重用**: 可在多个页面中使用
- **可配置**: 支持传入自定义初始代码
- **响应式**: 适配不同屏幕尺寸

## 在不同课程中使用

### 方法 1: 直接传入代码字符串

```blade
{{-- 数学课程示例 --}}
@livewire('run', ['initialCode' => '
# 数学课程 - 几何计算
import math

# 计算圆的面积和周长
radius = 5
area = math.pi * radius ** 2
circumference = 2 * math.pi * radius

print(f"半径: {radius}")
print(f"面积: {area:.2f}")
print(f"周长: {circumference:.2f}")
'])
```

### 方法 2: 通过组件方法生成（推荐）

在父组件中定义方法：

```php
public function getCourseSpecificCode()
{
    // 根据课程类型返回不同代码
    $courseType = $this->getCourseType();
    
    switch ($courseType) {
        case 'math':
            return $this->getMathCourseCode();
        case 'data-science':
            return $this->getDataScienceCourseCode();
        case 'web-scraping':
            return $this->getWebScrapingCourseCode();
        default:
            return $this->getBasicCourseCode();
    }
}

private function getMathCourseCode()
{
    return "# 数学课程 - Python 实践\n" .
           "import math\n\n" .
           "# 计算三角函数\n" .
           "angle = 45  # 度\n" .
           "radians = math.radians(angle)\n" .
           "print(f'sin({angle}°) = {math.sin(radians):.3f}')\n" .
           "print(f'cos({angle}°) = {math.cos(radians):.3f}')";
}
```

### 方法 3: 从数据库读取

```php
public function getCourseSpecificCode()
{
    $course = Course::where('slug', request()->route('course'))->first();
    return $course->sample_code ?? $this->getDefaultCode();
}
```

## 扩展示例

### 数据科学课程

```python
# 数据科学课程 - 数据分析入门
import numpy as np

# 创建数据集
data = [23, 45, 56, 78, 32, 67, 89, 12, 34, 56]
print("原始数据:", data)

# 基本统计
print(f"平均值: {np.mean(data):.2f}")
print(f"中位数: {np.median(data):.2f}")
print(f"标准差: {np.std(data):.2f}")

# 数据筛选
filtered = [x for x in data if x > 50]
print("大于50的数据:", filtered)
```

### Web 开发课程

```python
# Web 开发课程 - HTTP 状态码
status_codes = {
    200: "OK",
    404: "Not Found", 
    500: "Internal Server Error",
    403: "Forbidden"
}

print("HTTP 状态码说明:")
for code, message in status_codes.items():
    print(f"{code}: {message}")

# 模拟简单的路由
def handle_request(path):
    routes = {
        "/": "首页",
        "/about": "关于我们", 
        "/contact": "联系我们"
    }
    return routes.get(path, "页面未找到")

print(f"\n访问 '/': {handle_request('/')}")
print(f"访问 '/about': {handle_request('/about')}")
```

## 组件内部结构

### PHP 部分 (Livewire 组件)
```php
public $code = '';           // 当前代码
public $output = '';         // 执行输出
public $isRunning = false;   // 执行状态
public $initialCode = '';    // 初始代码

public function mount($initialCode = null)  // 组件初始化
public function runCode()                   // 执行代码
public function updateOutput($output)       // 更新输出
public function clearOutput()               // 清空输出
public function resetCode()                 // 重置代码
```

### JavaScript 部分 (Alpine.js + Pyodide)
```javascript
pythonRunner() {
    pyodide: null,              // Pyodide 实例
    pyodideReady: false,        // 环境状态
    output: '',                 // 本地输出
    examples: {...},            // 示例代码
    
    initPyodide(),              // 初始化环境
    executeCode(code),          // 执行代码
    loadExample(type)           // 加载示例
}
```

## 最佳实践

### 1. 代码组织
- 将课程特定的代码生成逻辑放在父组件中
- 使用数据库存储复杂的示例代码
- 为不同课程类型创建代码模板

### 2. 性能优化
- Pyodide 只在第一次使用时加载
- 预加载常用的 Python 包
- 使用 CDN 加速资源加载

### 3. 用户体验
- 提供有意义的初始代码
- 添加课程相关的注释和说明
- 确保代码示例与课程内容相关

### 4. 错误处理
- 提供友好的错误信息
- 处理网络连接问题
- 优雅降级处理

## 未来扩展可能性

1. **代码保存功能**: 允许用户保存和分享代码
2. **多文件支持**: 支持多个 Python 文件的项目
3. **包管理**: 动态安装 Python 包
4. **代码补全**: 集成智能代码提示
5. **可视化支持**: 集成 matplotlib 等绘图库
6. **协作功能**: 实时代码协作编辑
7. **版本控制**: 代码历史记录和回滚

这个独立的组件设计使得 Python 代码运行器可以轻松地在不同课程和页面中重复使用，同时保持了高度的可定制性和扩展性。
