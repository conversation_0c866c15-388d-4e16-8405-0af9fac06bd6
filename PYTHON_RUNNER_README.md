# Python 代码运行器实现文档

## 概述

我们在 Laravel Livewire 应用的 sidebar 区域成功实现了一个功能完整的 Python 代码运行器。该模块集成了以下技术：

- **Pyodide**: 在浏览器中运行 Python 代码的 WebAssembly 实现
- **Laravel Livewire**: 用于服务器端状态管理和实时更新
- **Alpine.js**: 用于客户端交互和状态管理
- **Flux UI**: 提供现代化的 UI 组件

## 主要功能

### 1. Python 代码编辑器
- 语法高亮的代码编辑区域
- 支持多行代码输入
- 实时同步到 Livewire 组件

### 2. 代码执行
- 点击"运行代码"按钮执行 Python 代码
- 支持键盘快捷键 (Ctrl+Enter / Cmd+Enter)
- 实时显示执行状态

### 3. 输出显示
- 实时显示 Python 代码的输出结果
- 支持 print() 语句输出
- 错误信息显示
- 返回值显示

### 4. 示例代码
- Hello World 示例
- 数学运算示例
- 列表操作示例
- 函数定义示例

### 5. 状态指示器
- Python 环境加载状态显示
- 代码执行状态显示
- 视觉反馈和加载动画

## 技术实现

### Livewire 组件 (PHP)
```php
public $code = "...";           // 用户输入的代码
public $output = '';            // 执行结果
public $isRunning = false;      // 执行状态

public function runCode()       // 触发代码执行
public function updateOutput() // 更新输出结果
public function clearOutput()  // 清空输出
```

### Alpine.js 组件 (JavaScript)
```javascript
pythonRunner() {
    pyodide: null,              // Pyodide 实例
    pyodideReady: false,        // 环境就绪状态
    output: '',                 // 本地输出缓存
    examples: {...},            // 示例代码集合
    
    initPyodide(),              // 初始化 Python 环境
    executeCode(),              // 执行 Python 代码
    loadExample()               // 加载示例代码
}
```

### Pyodide 集成
- 从 CDN 加载 Pyodide (v0.24.1)
- 配置 stdout/stderr 重定向
- 预加载常用包 (numpy)
- 错误处理和状态管理

## 用户体验特性

1. **渐进式加载**: Python 环境在后台异步加载，不阻塞页面
2. **实时反馈**: 加载状态和执行状态的视觉指示
3. **键盘快捷键**: Ctrl+Enter 快速执行代码
4. **示例代码**: 一键加载常用代码模板
5. **错误处理**: 友好的错误信息显示
6. **响应式设计**: 适配不同屏幕尺寸

## 文件结构

```
resources/views/livewire/course.blade.php
├── Livewire 组件定义 (PHP)
├── HTML 模板
│   ├── 主内容区域
│   └── Sidebar Python 运行器
│       ├── 标题和状态指示器
│       ├── 代码编辑器
│       ├── 控制按钮和示例
│       └── 输出显示区域
├── Pyodide CDN 加载
└── Alpine.js 组件脚本
```

## 使用方法

1. 访问课程页面 (如 `/liberation-from-regret`)
2. 在右侧 sidebar 的代码编辑器中输入 Python 代码
3. 点击"运行代码"按钮或按 Ctrl+Enter 执行
4. 查看下方的输出结果
5. 使用示例按钮快速加载预设代码

## 支持的 Python 功能

- 基础语法和数据类型
- 控制流 (if/else, for/while)
- 函数定义和调用
- 类和对象
- 标准库模块 (math, datetime, etc.)
- NumPy 数值计算 (预加载)
- 错误处理和异常

## 扩展可能性

1. **更多 Python 包**: 可以预加载更多科学计算包
2. **代码保存**: 实现代码的本地存储
3. **代码分享**: 生成代码分享链接
4. **语法高亮**: 集成代码编辑器 (如 CodeMirror)
5. **代码补全**: 添加智能代码提示
6. **文件操作**: 支持文件上传和下载
7. **可视化**: 集成 matplotlib 等绘图库

## 性能考虑

- Pyodide 首次加载约 10-20MB
- 后续代码执行在客户端进行，无服务器负载
- 支持离线使用 (加载完成后)
- 内存使用受浏览器限制

这个实现为学习者提供了一个完整的 Python 代码实验环境，无需安装任何软件即可在浏览器中学习和实践 Python 编程。
