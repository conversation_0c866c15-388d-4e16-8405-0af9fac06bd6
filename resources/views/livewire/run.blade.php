<?php

use Livewire\Volt\Component;

new class extends Component {
    public $code = "";
    public $output = "";
    public $isRunning = false;
    public $initialCode = "";

    public function mount($initialCode = null)
    {
        // 如果传入了初始代码，使用传入的代码，否则使用默认代码
        $this->initialCode = $initialCode ?? $this->getDefaultCode();
        $this->code = $this->initialCode;
    }

    public function runCode()
    {
        $this->isRunning = true;
        // 这里我们将使用前端的 Pyodide 来执行代码
        // 后端只是更新状态
        $this->dispatch("execute-python-code", code: $this->code);
    }

    public function updateOutput($output)
    {
        $this->output = $output;
        $this->isRunning = false;
    }

    public function clearOutput()
    {
        $this->output = "";
    }

    public function resetCode()
    {
        $this->code = $this->initialCode;
        $this->output = "";
    }

    private function getDefaultCode()
    {
        return "# 在这里编写 Python 代码\nprint('Hello, Python!')\n\n# 尝试一些数学运算\nimport math\nresult = math.sqrt(16)\nprint(f'平方根 16 = {result}')\n\n# 创建一个简单的列表\nnumbers = [1, 2, 3, 4, 5]\nprint(f'数字列表: {numbers}')\nprint(f'列表总和: {sum(numbers)}')";
    }
}; ?>

<div
    class="flex h-full flex-col bg-zinc-800 text-white"
    x-data="pythonRunner()"
    x-init="initPyodide()"
    @execute-python-code.window="executeCode($event.detail.code)"
>
    {{-- 标题栏 --}}
    <div class="border-b border-zinc-700 p-4">
        <div class="mb-2 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-white">Python 代码运行器</h3>
            <div class="flex items-center gap-2">
                <div x-show="!pyodideReady" class="flex items-center gap-1 text-xs text-yellow-400">
                    <svg class="h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    加载中
                </div>
                <div x-show="pyodideReady" class="flex items-center gap-1 text-xs text-green-400">
                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                    就绪
                </div>
            </div>
        </div>
        <p class="text-sm text-zinc-300">在下方编写 Python 代码并点击运行 (Ctrl+Enter)</p>
    </div>

    {{-- 代码编辑器 --}}
    <div class="flex flex-1 flex-col">
        <div class="border-b border-zinc-700 p-4">
            <label class="mb-2 block text-sm font-medium text-zinc-300">Python 代码:</label>
            <div class="h-48 w-full rounded-lg border border-zinc-600 bg-zinc-900" x-ref="aceContainer" x-init="initAceEditor()"></div>
            {{-- 隐藏的 textarea 用于 Livewire 绑定 --}}
            <textarea wire:model.live.debounce.500ms="code" x-ref="hiddenTextarea" class="hidden"></textarea>
        </div>

        {{-- 控制按钮 --}}
        <div class="border-b border-zinc-700 p-4">
            <div class="mb-3 flex gap-2">
                <flux:button wire:click="runCode" :disabled="$isRunning" variant="primary" size="sm" class="flex-1">
                    <span x-show="!$wire.isRunning">▶ 运行代码</span>
                    <span x-show="$wire.isRunning" class="flex items-center gap-2">
                        <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        运行中...
                    </span>
                </flux:button>

                <flux:button wire:click="resetCode" variant="ghost" size="sm" class="text-zinc-400 hover:text-white" title="重置为初始代码">
                    🔄 重置
                </flux:button>

                <flux:button wire:click="clearOutput" variant="ghost" size="sm" class="text-zinc-400 hover:text-white">🗑 清空</flux:button>
            </div>
        </div>

        {{-- 输出结果 --}}
        <div class="flex-1 p-4">
            <label class="mb-2 block text-sm font-medium text-zinc-300">运行结果:</label>
            <div class="h-48 overflow-y-auto rounded-lg border border-zinc-600 bg-zinc-900 p-3">
                <div x-show="!pyodideReady" class="text-sm text-zinc-400">
                    <div class="flex items-center gap-2">
                        <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        正在加载 Python 环境...
                    </div>
                </div>

                <div x-show="pyodideReady && !output && !$wire.output" class="text-sm text-zinc-500 italic">点击"运行代码"查看输出结果</div>

                <pre x-show="output || $wire.output" class="font-mono text-sm whitespace-pre-wrap text-green-400" x-text="output || $wire.output"></pre>
            </div>
        </div>
    </div>

    {{-- 加载 Ace Editor 作为替代方案 --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/ace.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/mode-python.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/theme-monokai.js"></script>

    {{-- 加载 Pyodide CDN --}}
    <script src="https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"></script>

    <script>
        // 在 Alpine.js 初始化之前定义数据
        document.addEventListener('alpine:init', () => {
            Alpine.data('pythonRunner', () => ({
                pyodide: null,
                pyodideReady: false,
                output: '',
                aceEditor: null,

                // 初始化 Ace Editor
                async initAceEditor() {
                    try {
                        console.log('开始初始化 Ace Editor...');

                        // 等待 Ace Editor 加载完成
                        let attempts = 0;
                        while (typeof ace === 'undefined' && attempts < 50) {
                            await new Promise((resolve) => setTimeout(resolve, 100));
                            attempts++;
                        }

                        if (typeof ace === 'undefined') {
                            throw new Error('Ace Editor 未加载');
                        }

                        // 创建 Ace Editor 实例
                        this.aceEditor = ace.edit(this.$refs.aceContainer);

                        // 配置编辑器
                        this.aceEditor.setTheme('ace/theme/monokai');
                        this.aceEditor.session.setMode('ace/mode/python');
                        this.aceEditor.setOptions({
                            fontSize: 14,
                            showPrintMargin: false,
                            highlightActiveLine: true,
                            enableBasicAutocompletion: true,
                            enableLiveAutocompletion: true,
                            tabSize: 4,
                            useSoftTabs: true,
                            wrap: true,
                            showLineNumbers: true,
                            showGutter: true,
                        });

                        // 设置初始值
                        this.aceEditor.setValue(this.$wire.code || '# 在这里编写 Python 代码...\nprint("Hello, Python!")', -1);

                        // 监听编辑器内容变化
                        this.aceEditor.session.on('change', () => {
                            const value = this.aceEditor.getValue();
                            this.$wire.set('code', value);
                        });

                        // 添加快捷键
                        this.aceEditor.commands.addCommand({
                            name: 'runCode',
                            bindKey: { win: 'Ctrl-Enter', mac: 'Cmd-Enter' },
                            exec: () => {
                                this.$wire.runCode();
                            },
                        });

                        // 监听 Livewire 的代码变化
                        this.$watch('$wire.code', (newCode) => {
                            if (this.aceEditor && newCode !== this.aceEditor.getValue()) {
                                this.aceEditor.setValue(newCode, -1);
                            }
                        });

                        console.log('Ace Editor 初始化成功！');
                    } catch (error) {
                        console.error('Ace Editor 初始化失败:', error);
                        this.fallbackToTextarea();
                    }
                },

                // 回退到简单的 textarea
                fallbackToTextarea() {
                    const container = this.$refs.aceContainer;
                    container.innerHTML = `
                        <textarea
                            class="h-full w-full resize-none bg-zinc-900 p-3 font-mono text-sm leading-5 text-white caret-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                            style="tab-size: 4; line-height: 1.5;"
                            placeholder="# 在这里编写 Python 代码...
# 按 Ctrl+Enter 快速运行"
                            x-model="$wire.code"
                            @keydown.ctrl.enter="$wire.runCode()"
                            @keydown.cmd.enter="$wire.runCode()"
                            spellcheck="false"
                        ></textarea>
                    `;
                },

                async initPyodide() {
                    try {
                        console.log('开始加载 Pyodide...');

                        // 等待 Pyodide 脚本加载完成
                        if (typeof loadPyodide === 'undefined') {
                            this.output = '正在加载 Python 环境，请稍候...';
                            // 等待 loadPyodide 函数可用
                            await new Promise((resolve) => {
                                const checkPyodide = () => {
                                    if (typeof loadPyodide !== 'undefined') {
                                        resolve();
                                    } else {
                                        setTimeout(checkPyodide, 100);
                                    }
                                };
                                checkPyodide();
                            });
                        }

                        // 加载 Pyodide
                        this.pyodide = await loadPyodide({
                            indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.24.1/full/',
                            stdout: (text) => {
                                this.output += text;
                            },
                            stderr: (text) => {
                                this.output += 'Error: ' + text;
                            },
                        });

                        console.log('Pyodide 加载完成');
                        this.pyodideReady = true;
                        this.output = 'Python 环境已准备就绪！\n';

                        // 预加载一些常用的包（可选）
                        try {
                            await this.pyodide.loadPackage(['numpy']);
                            console.log('NumPy 包加载完成');
                        } catch (error) {
                            console.log('NumPy 包加载失败，但基础功能仍可使用');
                        }
                    } catch (error) {
                        console.error('Pyodide 加载失败:', error);
                        this.output = 'Python 环境加载失败: ' + error.message;
                    }
                },

                async executeCode(code) {
                    if (!this.pyodideReady) {
                        this.output = 'Python 环境尚未准备就绪，请稍候...';
                        return;
                    }

                    if (!code.trim()) {
                        this.output = '请输入要执行的 Python 代码';
                        return;
                    }

                    try {
                        // 清空之前的输出
                        this.output = '';

                        // 执行 Python 代码
                        const result = this.pyodide.runPython(code);

                        // 如果有返回值且不是 None，显示返回值
                        if (result !== undefined && result !== null && result.toString() !== 'None') {
                            this.output += '\n>>> ' + result.toString();
                        }

                        // 如果没有任何输出，显示执行完成信息
                        if (!this.output.trim()) {
                            this.output = '代码执行完成（无输出）';
                        }

                        // 更新 Livewire 组件的输出
                        this.$wire.updateOutput(this.output);
                    } catch (error) {
                        console.error('Python 代码执行错误:', error);
                        this.output = 'Python 执行错误:\n' + error.message;
                        this.$wire.updateOutput(this.output);
                    }
                },
            }));
        });
    </script>
</div>
