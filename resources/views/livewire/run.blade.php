<?php

use Livewire\Volt\Component;

new class extends Component {
    public $code = "";
    public $output = "";
    public $isRunning = false;
    public $initialCode = "";

    public function mount($initialCode = null)
    {
        // 如果传入了初始代码，使用传入的代码，否则使用默认代码
        $this->initialCode = $initialCode ?? $this->getDefaultCode();
        $this->code = $this->initialCode;
    }

    public function runCode()
    {
        $this->isRunning = true;
        // 这里我们将使用前端的 Pyodide 来执行代码
        // 后端只是更新状态
        $this->dispatch("execute-python-code", code: $this->code);
    }

    public function updateOutput($output)
    {
        $this->output = $output;
        $this->isRunning = false;
    }

    public function clearOutput()
    {
        $this->output = "";
    }

    public function resetCode()
    {
        $this->code = $this->initialCode;
        $this->output = "";
    }

    private function getDefaultCode()
    {
        return "# 在这里编写 Python 代码\nprint('Hello, Python!')\n\n# 尝试一些数学运算\nimport math\nresult = math.sqrt(16)\nprint(f'平方根 16 = {result}')\n\n# 创建一个简单的列表\nnumbers = [1, 2, 3, 4, 5]\nprint(f'数字列表: {numbers}')\nprint(f'列表总和: {sum(numbers)}')";
    }
}; ?>

<div
    class="flex h-full flex-col bg-zinc-800 text-white"
    x-data="pythonRunner()"
    x-init="initPyodide()"
    @execute-python-code.window="executeCode($event.detail.code)"
>
    {{-- 标题栏 --}}
    <div class="border-b border-zinc-700 p-4">
        <div class="mb-2 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-white">Python 代码运行器</h3>
            <div class="flex items-center gap-2">
                <div x-show="!pyodideReady" class="flex items-center gap-1 text-xs text-yellow-400">
                    <svg class="h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    加载中
                </div>
                <div x-show="pyodideReady" class="flex items-center gap-1 text-xs text-green-400">
                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                    就绪
                </div>
            </div>
        </div>
        <p class="text-sm text-zinc-300">在下方编写 Python 代码并点击运行 (Ctrl+Enter)</p>
    </div>

    {{-- 代码编辑器 --}}
    <div class="flex flex-1 flex-col">
        <div class="border-b border-zinc-700 p-4">
            <label class="mb-2 block text-sm font-medium text-zinc-300">Python 代码:</label>
            <div class="h-48 w-full overflow-hidden rounded-lg border border-zinc-600 bg-zinc-900" x-ref="editorContainer" x-init="initCodeMirror()"></div>
            {{-- 隐藏的 textarea 用于 Livewire 绑定 --}}
            <textarea wire:model.live.debounce.500ms="code" x-ref="hiddenTextarea" class="hidden"></textarea>
        </div>

        {{-- 控制按钮 --}}
        <div class="border-b border-zinc-700 p-4">
            <div class="mb-3 flex gap-2">
                <flux:button wire:click="runCode" :disabled="$isRunning" variant="primary" size="sm" class="flex-1">
                    <span x-show="!$wire.isRunning">▶ 运行代码</span>
                    <span x-show="$wire.isRunning" class="flex items-center gap-2">
                        <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        运行中...
                    </span>
                </flux:button>

                <flux:button wire:click="resetCode" variant="ghost" size="sm" class="text-zinc-400 hover:text-white" title="重置为初始代码">
                    🔄 重置
                </flux:button>

                <flux:button wire:click="clearOutput" variant="ghost" size="sm" class="text-zinc-400 hover:text-white">🗑 清空</flux:button>
            </div>

            {{-- 示例代码按钮 --}}
            <div class="space-y-2">
                <p class="text-xs text-zinc-400">快速示例:</p>
                <div class="flex flex-wrap gap-1">
                    <button
                        @click="loadExample('hello')"
                        class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                    >
                        Hello World
                    </button>
                    <button
                        @click="loadExample('math')"
                        class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                    >
                        数学运算
                    </button>
                    <button
                        @click="loadExample('list')"
                        class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                    >
                        列表操作
                    </button>
                    <button
                        @click="loadExample('function')"
                        class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                    >
                        函数定义
                    </button>
                </div>
            </div>
        </div>

        {{-- 输出结果 --}}
        <div class="flex-1 p-4">
            <label class="mb-2 block text-sm font-medium text-zinc-300">运行结果:</label>
            <div class="h-48 overflow-y-auto rounded-lg border border-zinc-600 bg-zinc-900 p-3">
                <div x-show="!pyodideReady" class="text-sm text-zinc-400">
                    <div class="flex items-center gap-2">
                        <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        正在加载 Python 环境...
                    </div>
                </div>

                <div x-show="pyodideReady && !output && !$wire.output" class="text-sm text-zinc-500 italic">点击"运行代码"查看输出结果</div>

                <pre x-show="output || $wire.output" class="font-mono text-sm whitespace-pre-wrap text-green-400" x-text="output || $wire.output"></pre>
            </div>
        </div>
    </div>

    {{-- 加载 CodeMirror 6 CDN --}}
    <script type="module">
        import { EditorView, basicSetup } from 'https://cdn.jsdelivr.net/npm/codemirror@6/+esm';
        import { python } from 'https://cdn.jsdelivr.net/npm/@codemirror/lang-python@6/+esm';
        import { oneDark } from 'https://cdn.jsdelivr.net/npm/@codemirror/theme-one-dark@6/+esm';
        import { keymap } from 'https://cdn.jsdelivr.net/npm/@codemirror/view@6/+esm';

        // 将模块暴露到全局作用域
        window.CodeMirror = {
            EditorView,
            basicSetup,
            python,
            oneDark,
            keymap,
        };

        console.log('CodeMirror modules loaded:', window.CodeMirror);
    </script>

    {{-- 加载 Pyodide CDN --}}
    <script src="https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"></script>

    <script>
        // 在 Alpine.js 初始化之前定义数据
        document.addEventListener('alpine:init', () => {
            Alpine.data('pythonRunner', () => ({
                pyodide: null,
                pyodideReady: false,
                output: '',
                editor: null,

                // 示例代码
                examples: {
                    hello: `# Hello World 示例
print("Hello, Python!")
print("欢迎使用 Python 代码运行器！")

# 显示当前时间
import datetime
now = datetime.datetime.now()
print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")`,

                    math: `# 数学运算示例
import math

# 基本运算
a = 10
b = 3
print(f"{a} + {b} = {a + b}")
print(f"{a} - {b} = {a - b}")
print(f"{a} * {b} = {a * b}")
print(f"{a} / {b} = {a / b:.2f}")

# 数学函数
print(f"√{a} = {math.sqrt(a):.2f}")
print(f"sin(π/2) = {math.sin(math.pi/2)}")
print(f"2^3 = {2**3}")`,

                    list: `# 列表操作示例
# 创建列表
fruits = ['苹果', '香蕉', '橙子', '葡萄']
numbers = [1, 2, 3, 4, 5]

print("水果列表:", fruits)
print("数字列表:", numbers)

# 列表操作
fruits.append('草莓')
print("添加草莓后:", fruits)

# 列表推导式
squares = [x**2 for x in numbers]
print("平方数:", squares)

# 统计函数
print(f"数字总和: {sum(numbers)}")
print(f"最大值: {max(numbers)}")
print(f"最小值: {min(numbers)}")`,

                    function: `# 函数定义示例
def greet(name, age=None):
    """问候函数"""
    if age:
        return f"你好，{name}！你今年{age}岁。"
    else:
        return f"你好，{name}！"

def calculate_area(length, width):
    """计算矩形面积"""
    return length * width

def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 使用函数
print(greet("小明"))
print(greet("小红", 18))

area = calculate_area(5, 3)
print(f"矩形面积: {area}")

print("斐波那契数列前6项:")
for i in range(6):
    print(f"F({i}) = {fibonacci(i)}")`,
                },

                // 初始化 CodeMirror 编辑器
                async initCodeMirror() {
                    try {
                        console.log('开始初始化 CodeMirror...');

                        // 等待 CodeMirror 模块加载完成
                        let attempts = 0;
                        while (!window.CodeMirror && attempts < 50) {
                            await new Promise((resolve) => setTimeout(resolve, 100));
                            attempts++;
                        }

                        if (!window.CodeMirror) {
                            throw new Error('CodeMirror 模块加载超时');
                        }

                        const { EditorView, basicSetup, python, oneDark, keymap } = window.CodeMirror;
                        console.log('CodeMirror 模块已加载:', { EditorView, basicSetup, python, oneDark, keymap });

                        // 创建编辑器
                        this.editor = new EditorView({
                            doc: this.$wire.code || '# 在这里编写 Python 代码...\nprint("Hello, Python!")',
                            extensions: [
                                basicSetup,
                                python(),
                                oneDark,
                                keymap.of([
                                    {
                                        key: 'Ctrl-Enter',
                                        run: () => {
                                            this.$wire.runCode();
                                            return true;
                                        },
                                    },
                                    {
                                        key: 'Cmd-Enter',
                                        run: () => {
                                            this.$wire.runCode();
                                            return true;
                                        },
                                    },
                                ]),
                                EditorView.updateListener.of((update) => {
                                    if (update.docChanged) {
                                        const code = update.state.doc.toString();
                                        this.$wire.set('code', code);
                                    }
                                }),
                            ],
                            parent: this.$refs.editorContainer,
                        });

                        console.log('CodeMirror 编辑器创建成功:', this.editor);

                        // 监听 Livewire 的代码变化
                        this.$watch('$wire.code', (newCode) => {
                            if (this.editor && newCode !== this.editor.state.doc.toString()) {
                                this.editor.dispatch({
                                    changes: {
                                        from: 0,
                                        to: this.editor.state.doc.length,
                                        insert: newCode,
                                    },
                                });
                            }
                        });
                    } catch (error) {
                        console.error('CodeMirror 初始化失败:', error);
                        // 如果失败，回退到简单的 textarea
                        this.fallbackToTextarea();
                    }
                },

                // 回退到简单的 textarea
                fallbackToTextarea() {
                    const container = this.$refs.editorContainer;
                    container.innerHTML = `
                        <textarea
                            class="h-full w-full resize-none bg-zinc-900 p-3 font-mono text-sm leading-5 text-white caret-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                            style="tab-size: 4; line-height: 1.5;"
                            placeholder="# 在这里编写 Python 代码...
# 按 Ctrl+Enter 快速运行"
                            x-model="$wire.code"
                            @keydown.ctrl.enter="$wire.runCode()"
                            @keydown.cmd.enter="$wire.runCode()"
                            spellcheck="false"
                        ></textarea>
                    `;
                },

                async initPyodide() {
                    try {
                        console.log('开始加载 Pyodide...');

                        // 等待 Pyodide 脚本加载完成
                        if (typeof loadPyodide === 'undefined') {
                            this.output = '正在加载 Python 环境，请稍候...';
                            // 等待 loadPyodide 函数可用
                            await new Promise((resolve) => {
                                const checkPyodide = () => {
                                    if (typeof loadPyodide !== 'undefined') {
                                        resolve();
                                    } else {
                                        setTimeout(checkPyodide, 100);
                                    }
                                };
                                checkPyodide();
                            });
                        }

                        // 加载 Pyodide
                        this.pyodide = await loadPyodide({
                            indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.24.1/full/',
                            stdout: (text) => {
                                this.output += text;
                            },
                            stderr: (text) => {
                                this.output += 'Error: ' + text;
                            },
                        });

                        console.log('Pyodide 加载完成');
                        this.pyodideReady = true;
                        this.output = 'Python 环境已准备就绪！\n';

                        // 预加载一些常用的包（可选）
                        try {
                            await this.pyodide.loadPackage(['numpy']);
                            console.log('NumPy 包加载完成');
                        } catch (error) {
                            console.log('NumPy 包加载失败，但基础功能仍可使用');
                        }
                    } catch (error) {
                        console.error('Pyodide 加载失败:', error);
                        this.output = 'Python 环境加载失败: ' + error.message;
                    }
                },

                async executeCode(code) {
                    if (!this.pyodideReady) {
                        this.output = 'Python 环境尚未准备就绪，请稍候...';
                        return;
                    }

                    if (!code.trim()) {
                        this.output = '请输入要执行的 Python 代码';
                        return;
                    }

                    try {
                        // 清空之前的输出
                        this.output = '';

                        // 执行 Python 代码
                        const result = this.pyodide.runPython(code);

                        // 如果有返回值且不是 None，显示返回值
                        if (result !== undefined && result !== null && result.toString() !== 'None') {
                            this.output += '\n>>> ' + result.toString();
                        }

                        // 如果没有任何输出，显示执行完成信息
                        if (!this.output.trim()) {
                            this.output = '代码执行完成（无输出）';
                        }

                        // 更新 Livewire 组件的输出
                        this.$wire.updateOutput(this.output);
                    } catch (error) {
                        console.error('Python 代码执行错误:', error);
                        this.output = 'Python 执行错误:\n' + error.message;
                        this.$wire.updateOutput(this.output);
                    }
                },

                // 加载示例代码
                loadExample(type) {
                    if (this.examples[type]) {
                        const code = this.examples[type];
                        this.$wire.set('code', code);

                        // 如果 CodeMirror 编辑器已准备好，更新编辑器内容
                        if (this.editor) {
                            this.editor.dispatch({
                                changes: {
                                    from: 0,
                                    to: this.editor.state.doc.length,
                                    insert: code,
                                },
                            });
                        }

                        this.output = '';
                        this.$wire.updateOutput('');
                    }
                },
            }));
        });
    </script>
</div>
